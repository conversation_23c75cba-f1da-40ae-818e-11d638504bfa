<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-opacity-50 z-[70] flex items-center justify-center p-4"
    @click="handleBackdropClick"
  >
    <div
      class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[80vh] flex flex-col"
      @click.stop
    >
      <!-- 弹窗头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900">选择车辆</h3>
        <button
          @click="handleClose"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- 搜索栏 -->
      <div class="p-6 border-b border-gray-200">
        <div class="relative">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索车牌号、司机姓名..."
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <svg
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <!-- 车辆列表 -->
      <div class="flex-1 overflow-y-auto p-6">
        <div v-if="loading" class="flex items-center justify-center h-32">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>

        <div v-else-if="filteredVehicles.length === 0" class="text-center py-12">
          <svg
            class="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p class="mt-2 text-sm text-gray-500">暂无车辆数据</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="vehicle in paginatedVehicles"
            :key="vehicle.device_code"
            class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
            :class="{ 'border-blue-500 bg-blue-50': selectedVehicle?.device_code === vehicle.device_code }"
            @click="selectVehicle(vehicle)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-4 mb-3">
                  <div class="flex items-center space-x-2">
                    <span class="text-lg font-semibold text-gray-900">
                      {{ vehicle.license_plate }}
                    </span>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      已关联
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    距离: {{ vehicle.distance ? vehicle.distance + 'km' : '未知' }}
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500">载重:</span>
                    <span class="ml-1 font-medium">{{ vehicle.rated_load_kg || '-' }}kg</span>
                  </div>
                  <div>
                    <span class="text-gray-500">容积:</span>
                    <span class="ml-1 font-medium">{{ vehicle.vehicle_volume || '-' }}m³</span>
                  </div>
                  <div>
                    <span class="text-gray-500">司机:</span>
                    <span class="ml-1 font-medium">{{ vehicle.driver_name || '未分配' }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">司机编号:</span>
                    <span class="ml-1 font-medium">{{ vehicle.driver_code || '-' }}</span>
                  </div>
                </div>

                <div class="mt-2 text-sm">
                  <span class="text-gray-500">设备编号:</span>
                  <span class="ml-1 font-medium">{{ vehicle.device_code || '-' }}</span>
                </div>
              </div>

              <!-- 关联图标 -->
              <div class="ml-4 flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg
                    class="w-4 h-4 text-blue-600"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            共 {{ filteredVehicles.length }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
          </div>
          <div class="flex space-x-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            <button
              @click="currentPage++"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
        <button
          @click="handleClose"
          class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="!selectedVehicle"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          确认选择
        </button>
      </div>
    </div>

    <!-- 司机关联确认弹窗 -->
    <div
      v-if="showDriverConfirm"
      class="fixed inset-0 bg-black bg-opacity-50 z-[80] flex items-center justify-center p-4"
      @click.stop
    >
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <svg
              class="w-5 h-5 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h4 class="text-lg font-semibold text-gray-900">确认关联</h4>
        </div>
        <p class="text-gray-600 mb-6">
          是否同步关联司机信息？
        </p>
        <div class="flex space-x-3">
          <button
            @click="confirmWithoutDriver"
            class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button
            @click="confirmWithDriver"
            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { getFreeCarApi } from '@/api/abnormal'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  abnormalPoint: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'confirm'])

// 响应式数据
const loading = ref(false)
const vehicles = ref([])
const selectedVehicle = ref(null)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const showDriverConfirm = ref(false)

// 计算属性
const filteredVehicles = computed(() => {
  if (!searchKeyword.value) return vehicles.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return vehicles.value.filter(vehicle => 
    vehicle.license_plate?.toLowerCase().includes(keyword) ||
    vehicle.driver_name?.toLowerCase().includes(keyword) ||
    vehicle.driver_code?.toLowerCase().includes(keyword)
  )
})

const totalPages = computed(() => {
  return Math.ceil(filteredVehicles.value.length / pageSize.value)
})

const paginatedVehicles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredVehicles.value.slice(start, end)
})

// 方法
const handleBackdropClick = (e) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}

const handleClose = () => {
  selectedVehicle.value = null
  searchKeyword.value = ''
  currentPage.value = 1
  showDriverConfirm.value = false
  emit('close')
}

const selectVehicle = (vehicle) => {
  selectedVehicle.value = vehicle
}

const handleConfirm = () => {
  if (!selectedVehicle.value) return
  
  // 如果选中的车辆有司机信息，显示确认弹窗
  if (selectedVehicle.value.driver_name) {
    showDriverConfirm.value = true
  } else {
    // 没有司机信息，直接确认
    confirmWithoutDriver()
  }
}

const confirmWithDriver = () => {
  emit('confirm', {
    vehicle: selectedVehicle.value,
    includeDriver: true
  })
  handleClose()
}

const confirmWithoutDriver = () => {
  emit('confirm', {
    vehicle: selectedVehicle.value,
    includeDriver: false
  })
  handleClose()
}

// 计算距离的函数
const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371 // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return (R * c).toFixed(2)
}

// 处理车辆数据
const processVehicleData = (freeCarData) => {
  if (!freeCarData?.result || !Array.isArray(freeCarData.result) || freeCarData.result.length < 2) {
    return []
  }

  const [vehicleList, locationList] = freeCarData.result
  const processedVehicles = []

  vehicleList.forEach(vehicle => {
    // 根据device_code匹配位置信息
    const location = locationList.find(loc => loc.imei === vehicle.device_code)

    if (location) {
      let distance = 0
      console.log('location',props.abnormalPoint);
      
      // 如果有异常点坐标，计算距离
      if (props.abnormalPoint?.exception_location_coordinates) {
        try {
          const [abnormalLat, abnormalLng] = props.abnormalPoint.exception_location_coordinates.split(',').map(Number)
          distance = calculateDistance(abnormalLat, abnormalLng, location.lat_tx, location.lng_tx)
        } catch (error) {
          console.error('计算距离失败:', error)
          distance = 0
        }
      }

      processedVehicles.push({
        ...vehicle,
        lat_tx: location.lat_tx,
        lng_tx: location.lng_tx,
        distance: parseFloat(distance)
      })
    }
  })

  // 按距离排序
  return processedVehicles.sort((a, b) => a.distance - b.distance)
}

// 获取车辆数据
const fetchVehicleData = async () => {
  loading.value = true
  try {
    const response = await getFreeCarApi()
    vehicles.value = processVehicleData(response)
  } catch (error) {
    console.error('获取车辆数据失败:', error)
    // 使用模拟数据作为备用
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchVehicleData()
  }
})

// 重置搜索时回到第一页
watch(searchKeyword, () => {
  currentPage.value = 1
})

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// 清理键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
